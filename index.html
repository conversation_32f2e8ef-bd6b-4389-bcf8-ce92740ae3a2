<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Reels Collection</title>
    
    <!-- External Libraries -->
    <!-- Swiper.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Animation -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-animation">
            <div class="liquid-loader"></div>
            <p>Loading your collection...</p>
        </div>
    </div>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo" onclick="showAllCardsView()" style="cursor: pointer;">
                    <i class="fab fa-instagram"></i>
                    <h1>Reels Collection</h1>
                    <div class="install-message">
                        <span id="typing-text">Install Instagram for best experience</span>
                    </div>
                </div>
                <div class="header-actions">
                    <button id="header-video-reels-btn" class="header-nav-btn" style="display: none;">
                        <i class="fas fa-video"></i>
                        <span>Video Reels</span>
                    </button>
                    <button id="add-reel-btn" class="add-btn">
                        <i class="fas fa-plus"></i>
                        <span>Add Link</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Search and Filter Section -->
            <div class="search-section">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="search-input" placeholder="Search your reels...">
                </div>
                <div class="filter-tags" id="filter-tags">
                    <!-- Dynamic tags will be added here -->
                </div>
            </div>

            <!-- Empty State -->
            <div id="empty-state" class="empty-state">
                <div class="empty-animation">
                    <i class="fab fa-instagram"></i>
                </div>
                <h2>Start Your Collection</h2>
                <p>Add your first Instagram reel to begin building your amazing collection!</p>
                <button class="empty-add-btn" onclick="openAddModal()">
                    <i class="fas fa-plus"></i>
                    Add Your First Reel
                </button>
            </div>

            <!-- Video Reels Section -->
            <div id="video-reels-section" class="video-reels-section" style="display: none;">
                <div class="section-header">
                    <h2>
                        <i class="fas fa-video"></i>
                        Video Reels
                    </h2>
                    <button id="show-all-cards" class="view-toggle-btn">
                        <i class="fas fa-th"></i>
                        Show All Cards
                    </button>
                </div>
                <div id="video-reels-grid" class="video-reels-grid">
                    <!-- Video reels will be added here -->
                </div>
            </div>

            <!-- Cards Grid -->
            <div id="cards-grid" class="cards-grid">
                <!-- Dynamic cards will be added here -->
            </div>

            <!-- View Toggle -->
            <div id="view-toggle" class="view-toggle" style="display: none;">
                <button id="show-video-reels" class="view-toggle-btn">
                    <i class="fas fa-video"></i>
                    Video Reels View
                </button>
            </div>
        </div>
    </main>

    <!-- Add Reel Modal -->
    <div id="add-modal" class="modal">
        <div class="modal-overlay" onclick="closeAddModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Reel</h2>
                <button class="close-btn" onclick="closeAddModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-reel-form" class="add-form">
                <div class="form-group">
                    <label for="reel-title">Title</label>
                    <input type="text" id="reel-title" name="title" placeholder="Enter reel title..." required>
                </div>
                
                <div class="form-group">
                    <label for="reel-image">Image Preview (ImgBB Link)</label>
                    <input type="url" id="reel-image" name="image" placeholder="https://i.ibb.co/..." required>
                    <small>Upload your screenshot to ImgBB and paste the direct link here</small>
                </div>
                
                <div class="form-group">
                    <label for="reel-url">Instagram Reel URL</label>
                    <input type="url" id="reel-url" name="url" placeholder="https://www.instagram.com/reel/..." required>
                </div>

                <div class="form-group">
                    <label for="video-embed">Video Embed Code (Mega)</label>
                    <textarea id="video-embed" name="videoEmbed" rows="3" placeholder='<iframe width="640" height="360" frameborder="0" src="https://mega.nz/embed/..." allowfullscreen></iframe>'></textarea>
                    <small>Paste Mega embed iframe code here (optional)</small>
                </div>

                <div class="form-group">
                    <label for="reel-tags">Tags</label>
                    <input type="text" id="reel-tags" name="tags" placeholder="funny, dance, trending (comma separated)">
                    <small>Separate tags with commas</small>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="closeAddModal()">Cancel</button>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-plus"></i>
                        Add Reel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Detail Modal -->
    <div id="detail-modal" class="modal detail-modal">
        <div class="modal-overlay" onclick="closeDetailModal()"></div>
        <div class="modal-content detail-content">
            <button class="close-btn detail-close" onclick="closeDetailModal()">
                <i class="fas fa-times"></i>
            </button>
            
            <div class="detail-layout">
                <div class="detail-left">
                    <div class="media-container">
                        <div class="image-container">
                            <img id="detail-image" src="" alt="Reel Preview">
                            <div class="zoom-overlay"></div>
                        </div>
                        <div class="video-container" id="video-container" style="display: none;">
                            <div id="mega-embed-container" class="mega-embed-container">
                                <!-- Mega embed iframe will be inserted here -->
                            </div>
                        </div>
                        <div class="media-toggle">
                            <button id="show-image-btn" class="toggle-btn active">
                                <i class="fas fa-image"></i>
                                Image
                            </button>
                            <button id="show-video-btn" class="toggle-btn" style="display: none;">
                                <i class="fas fa-video"></i>
                                Video
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="detail-right">
                    <div class="detail-info">
                        <h2 id="detail-title"></h2>
                        <div id="detail-tags" class="detail-tags"></div>
                        <div class="detail-url">
                            <i class="fab fa-instagram"></i>
                            <span id="detail-url-text"></span>
                        </div>
                    </div>
                    
                    <div class="detail-actions">
                        <button id="open-link-btn" class="action-btn primary">
                            <i class="fas fa-external-link-alt"></i>
                            Open Link
                        </button>

                        <div class="install-suggestion">
                            <i class="fas fa-info-circle"></i>
                            <span>Install Instagram for best experience</span>
                        </div>

                        <button id="copy-link-btn" class="action-btn secondary">
                            <i class="fas fa-copy"></i>
                            Copy Link
                        </button>

                        <button id="download-btn" class="action-btn download">
                            <img src="https://sssinstagram.com/favicon.ico" alt="SSS" class="favicon">
                            Download Video
                        </button>

                        <button id="delete-btn" class="action-btn danger">
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Floating Instagram Icons Background -->
    <div id="floating-icons" class="floating-icons"></div>

    <!-- Mouse Follower Icon -->
    <div id="mouse-follower" class="mouse-follower">
        <i class="fab fa-instagram"></i>
    </div>

    <!-- Custom Confirmation Modal -->
    <div id="confirm-modal" class="modal confirm-modal">
        <div class="modal-overlay" onclick="closeConfirmModal()"></div>
        <div class="modal-content confirm-content">
            <div class="confirm-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 id="confirm-title">Are you sure?</h3>
            <p id="confirm-message">This action cannot be undone.</p>
            <div class="confirm-actions">
                <button id="confirm-cancel" class="confirm-btn cancel">Cancel</button>
                <button id="confirm-delete" class="confirm-btn delete">Delete</button>
            </div>
        </div>
    </div>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

    <!-- Custom JavaScript -->
    <script src="script.js"></script>
</body>
</html>
