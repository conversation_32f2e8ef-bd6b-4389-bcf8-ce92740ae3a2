// Global Variables
let reelsData = [];
let currentReelId = null;

// DOM Elements
const addModal = document.getElementById('add-modal');
const detailModal = document.getElementById('detail-modal');
const addForm = document.getElementById('add-reel-form');
const cardsGrid = document.getElementById('cards-grid');
const emptyState = document.getElementById('empty-state');
const searchInput = document.getElementById('search-input');
const filterTags = document.getElementById('filter-tags');
const loadingScreen = document.getElementById('loading-screen');

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    initializeFloatingIcons();
    initializeMouseFollower();
    initializeCustomEffects();
});

function initializeApp() {
    // Add animation styles
    addAnimationStyles();

    // Hide loading screen after a short delay
    setTimeout(() => {
        loadingScreen.classList.add('hidden');
    }, 1500);

    // Load data from localStorage
    loadReelsData();

    // Setup event listeners
    setupEventListeners();

    // Render initial state
    renderCardsEnhanced();
    renderFilterTags();
    renderVideoReels();

    // Initialize particles background
    createParticles();

    // Initialize enhanced features
    initializeEnhancedFeatures();

    // Initialize typing animation
    initializeTypingAnimation();
}

function setupEventListeners() {
    // Add reel button
    document.getElementById('add-reel-btn').addEventListener('click', openAddModalEnhanced);

    // Form submission
    addForm.addEventListener('submit', handleFormSubmit);

    // Search functionality
    searchInput.addEventListener('input', handleSearchEnhanced);

    // View toggle buttons
    document.getElementById('show-video-reels').addEventListener('click', showVideoReelsView);
    document.getElementById('show-all-cards').addEventListener('click', showAllCardsView);
    document.getElementById('header-video-reels-btn').addEventListener('click', showVideoReelsView);

    // Close modals on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAddModal();
            closeDetailModal();
        }
    });
}

// Modal Functions
function openAddModal() {
    addModal.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    // Focus on first input
    setTimeout(() => {
        document.getElementById('reel-title').focus();
    }, 100);
}

function closeAddModal() {
    addModal.classList.remove('active');
    document.body.style.overflow = 'auto';
    addForm.reset();
}

function openDetailModal(reelId) {
    openDetailModalEnhanced(reelId);
}

function closeDetailModal() {
    detailModal.classList.remove('active');
    document.body.style.overflow = 'auto';
    currentReelId = null;
}

function setupActionButtons(reel) {
    // Open link button
    document.getElementById('open-link-btn').onclick = () => {
        window.open(reel.url, '_blank');
    };
    
    // Copy link button
    document.getElementById('copy-link-btn').onclick = () => {
        copyToClipboard(reel.url);
    };
    
    // Download button
    document.getElementById('download-btn').onclick = () => {
        const downloadUrl = `https://sssinstagram.com/reels-downloader?url=${encodeURIComponent(reel.url)}`;
        window.open(downloadUrl, '_blank');
    };
    
    // Delete button
    document.getElementById('delete-btn').onclick = () => {
        showConfirmModal(
            'Delete Reel?',
            'This reel will be permanently removed from your collection.',
            () => {
                deleteReel(reel.id);
                closeDetailModal();
            }
        );
    };
}

// Form Handling
function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(addForm);
    const reelData = {
        id: generateId(),
        title: formData.get('title').trim(),
        image: formData.get('image').trim(),
        url: formData.get('url').trim(),
        videoEmbed: formData.get('videoEmbed') ? formData.get('videoEmbed').trim() : '',
        tags: formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag),
        createdAt: new Date().toISOString()
    };
    
    // Validate data
    if (!validateReelData(reelData)) {
        return;
    }
    
    // Add to data array
    reelsData.unshift(reelData);
    
    // Save to localStorage
    saveReelsData();
    
    // Re-render
    renderCardsEnhanced();
    renderFilterTags();
    renderVideoReels();

    // Close modal
    closeAddModal();

    // Show success message
    showToastEnhanced('Reel added successfully!', 'success');
}

function validateReelData(data) {
    if (!data.title) {
        showToastEnhanced('Please enter a title', 'error');
        return false;
    }

    if (!data.image || !isValidUrl(data.image)) {
        showToastEnhanced('Please enter a valid image URL', 'error');
        return false;
    }

    if (!data.url || !isValidInstagramUrl(data.url)) {
        showToastEnhanced('Please enter a valid Instagram reel URL', 'error');
        return false;
    }

    if (data.videoEmbed && !validateVideoEmbed(data.videoEmbed)) {
        showToastEnhanced('Please enter a valid Mega embed code', 'error');
        return false;
    }

    return true;
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function isValidInstagramUrl(url) {
    return url.includes('instagram.com') && (url.includes('/reel/') || url.includes('/p/'));
}

// Data Management
function loadReelsData() {
    const stored = localStorage.getItem('instagramReels');
    if (stored) {
        try {
            reelsData = JSON.parse(stored);
        } catch (e) {
            console.error('Error loading data:', e);
            reelsData = [];
        }
    }
}

function saveReelsData() {
    localStorage.setItem('instagramReels', JSON.stringify(reelsData));
}

function deleteReel(id) {
    reelsData = reelsData.filter(reel => reel.id !== id);
    saveReelsData();
    renderCardsEnhanced();
    renderFilterTags();
    renderVideoReels();
    showToastEnhanced('Reel deleted successfully', 'info');
}

// Rendering Functions
function renderCards(filteredData = null) {
    const dataToRender = filteredData || reelsData;
    
    if (dataToRender.length === 0) {
        emptyState.style.display = 'block';
        cardsGrid.style.display = 'none';
        return;
    }
    
    emptyState.style.display = 'none';
    cardsGrid.style.display = 'grid';
    
    cardsGrid.innerHTML = '';
    
    dataToRender.forEach(reel => {
        const cardElement = createCardElement(reel);
        cardsGrid.appendChild(cardElement);
    });
}

function createCardElement(reel) {
    const card = document.createElement('div');
    card.className = 'reel-card';
    card.onclick = () => openDetailModal(reel.id);
    
    card.innerHTML = `
        <img src="${reel.image}" alt="${reel.title}" class="card-image" loading="lazy">
        <div class="card-content">
            <h3 class="card-title">${reel.title}</h3>
            <div class="card-tags">
                ${reel.tags.map(tag => `<span class="card-tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
    
    return card;
}

function renderFilterTags() {
    const allTags = [...new Set(reelsData.flatMap(reel => reel.tags))];
    
    filterTags.innerHTML = '';
    
    if (allTags.length === 0) return;
    
    // Add "All" tag
    const allTag = document.createElement('span');
    allTag.className = 'filter-tag active';
    allTag.textContent = 'All';
    allTag.onclick = (e) => filterByTag('all', e.target);
    filterTags.appendChild(allTag);

    // Add individual tags
    allTags.forEach(tag => {
        const tagElement = document.createElement('span');
        tagElement.className = 'filter-tag';
        tagElement.textContent = tag;
        tagElement.onclick = (e) => filterByTag(tag, e.target);
        filterTags.appendChild(tagElement);
    });
}

// Search and Filter Functions
function handleSearch() {
    const query = searchInput.value.toLowerCase().trim();
    
    if (!query) {
        renderCards();
        return;
    }
    
    const filtered = reelsData.filter(reel => 
        reel.title.toLowerCase().includes(query) ||
        reel.tags.some(tag => tag.toLowerCase().includes(query))
    );
    
    renderCards(filtered);
}

function filterByTag(tag, element) {
    // Update active tag
    document.querySelectorAll('.filter-tag').forEach(el => {
        el.classList.remove('active');
    });

    if (element) {
        element.classList.add('active');
    }

    if (tag === 'all') {
        renderCardsEnhanced();
        return;
    }

    const filtered = reelsData.filter(reel => reel.tags.includes(tag));
    renderCardsEnhanced(filtered);
}

// Utility Functions
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showToastEnhanced('Link copied to clipboard!', 'success');
    }).catch(() => {
        showToastEnhanced('Failed to copy link', 'error');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    document.getElementById('toast-container').appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Enhanced Animations and Interactions
function initializeAnimations() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'cardSlideIn 0.6s ease forwards';
                cardObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all cards
    document.querySelectorAll('.reel-card').forEach(card => {
        cardObserver.observe(card);
    });
}

// Add CSS animation keyframes dynamically
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes cardSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .reel-card {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
        }

        .card-hover-effect {
            position: relative;
            overflow: hidden;
        }

        .card-hover-effect::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s ease;
            z-index: 1;
        }

        .card-hover-effect:hover::before {
            left: 100%;
        }

        .floating-action {
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .pulse-effect {
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .shake-effect {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(style);
}

// Enhanced card creation with animations
function createCardElementEnhanced(reel) {
    const card = document.createElement('div');
    card.className = 'reel-card card-hover-effect';
    card.onclick = () => openDetailModal(reel.id);

    // Add loading state
    card.innerHTML = `
        <div class="card-image-container">
            <img src="${reel.image}" alt="${reel.title}" class="card-image" loading="lazy"
                 onload="this.style.opacity='1'" style="opacity:0; transition: opacity 0.3s ease;">
            <div class="card-overlay">
                <i class="fas fa-play-circle"></i>
            </div>
        </div>
        <div class="card-content">
            <h3 class="card-title">${reel.title}</h3>
            <div class="card-tags">
                ${reel.tags.map(tag => `<span class="card-tag">${tag}</span>`).join('')}
            </div>
            <div class="card-meta">
                <span class="card-date">${formatDate(reel.createdAt)}</span>
            </div>
        </div>
    `;

    // Add hover effects
    card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-10px) scale(1.02)';
    });

    card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0) scale(1)';
    });

    return card;
}

// Enhanced modal animations
function openAddModalEnhanced() {
    addModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animate modal content
    const modalContent = addModal.querySelector('.modal-content');
    modalContent.style.animation = 'modalBounceIn 0.5s ease';

    // Focus on first input with delay
    setTimeout(() => {
        document.getElementById('reel-title').focus();
    }, 200);
}

function openDetailModalEnhanced(reelId) {
    const reel = reelsData.find(r => r.id === reelId);
    if (!reel) return;

    currentReelId = reelId;

    // Populate modal content
    document.getElementById('detail-image').src = reel.image;
    document.getElementById('detail-title').textContent = reel.title;
    document.getElementById('detail-url-text').textContent = reel.url;

    // Setup video embed if available
    setupVideoEmbed(reel);

    // Render tags with animation
    const detailTagsContainer = document.getElementById('detail-tags');
    detailTagsContainer.innerHTML = '';
    reel.tags.forEach((tag, index) => {
        const tagElement = document.createElement('span');
        tagElement.className = 'detail-tag';
        tagElement.textContent = tag;
        tagElement.style.animation = `tagSlideIn 0.3s ease ${index * 0.1}s forwards`;
        tagElement.style.opacity = '0';
        detailTagsContainer.appendChild(tagElement);
    });

    // Setup action buttons
    setupActionButtons(reel);

    // Setup media toggle
    setupMediaToggle(reel);

    detailModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Animate modal content
    const modalContent = detailModal.querySelector('.modal-content');
    modalContent.style.animation = 'modalSlideIn 0.4s ease';
}

// Utility function for date formatting
function formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
}

// Enhanced search with debouncing
let searchTimeout;
function handleSearchEnhanced() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        const query = searchInput.value.toLowerCase().trim();

        if (!query) {
            renderCardsEnhanced();
            return;
        }

        const filtered = reelsData.filter(reel =>
            reel.title.toLowerCase().includes(query) ||
            reel.tags.some(tag => tag.toLowerCase().includes(query))
        );

        renderCardsEnhanced(filtered);

        // Show search results count
        if (filtered.length === 0) {
            showToastEnhanced(`No results found for "${query}"`, 'info');
        }
    }, 300);
}

// Enhanced rendering with animations
function renderCardsEnhanced(filteredData = null) {
    const dataToRender = filteredData || reelsData;

    if (dataToRender.length === 0) {
        emptyState.style.display = 'block';
        cardsGrid.style.display = 'none';
        return;
    }

    emptyState.style.display = 'none';
    cardsGrid.style.display = 'grid';

    // Fade out existing cards
    const existingCards = cardsGrid.querySelectorAll('.reel-card');
    existingCards.forEach(card => {
        card.style.animation = 'fadeOut 0.2s ease forwards';
    });

    setTimeout(() => {
        cardsGrid.innerHTML = '';

        dataToRender.forEach((reel, index) => {
            const cardElement = createCardElementEnhanced(reel);
            cardElement.style.animationDelay = `${index * 0.1}s`;
            cardsGrid.appendChild(cardElement);
        });

        // Initialize animations for new cards
        initializeAnimations();
    }, 200);
}

// Particle Background Effect
function createParticles() {
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles';
    document.body.appendChild(particlesContainer);

    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 10 + 5) + 's';
        particle.style.animationDelay = Math.random() * 5 + 's';

        particlesContainer.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 15000);
    }

    // Create initial particles
    for (let i = 0; i < 20; i++) {
        setTimeout(() => createParticle(), i * 200);
    }

    // Continue creating particles
    setInterval(createParticle, 1000);
}

// Enhanced form validation with visual feedback
function validateFormField(field, value, type) {
    const input = document.getElementById(field);

    switch (type) {
        case 'title':
            if (!value || value.length < 3) {
                input.classList.add('error');
                input.classList.remove('success');
                return false;
            }
            break;
        case 'url':
            if (!value || !isValidUrl(value)) {
                input.classList.add('error');
                input.classList.remove('success');
                return false;
            }
            break;
        case 'instagram':
            if (!value || !isValidInstagramUrl(value)) {
                input.classList.add('error');
                input.classList.remove('success');
                return false;
            }
            break;
    }

    input.classList.remove('error');
    input.classList.add('success');
    return true;
}

// Real-time form validation
function setupFormValidation() {
    const titleInput = document.getElementById('reel-title');
    const imageInput = document.getElementById('reel-image');
    const urlInput = document.getElementById('reel-url');

    titleInput.addEventListener('input', (e) => {
        validateFormField('reel-title', e.target.value, 'title');
    });

    imageInput.addEventListener('input', (e) => {
        validateFormField('reel-image', e.target.value, 'url');
    });

    urlInput.addEventListener('input', (e) => {
        validateFormField('reel-url', e.target.value, 'instagram');
    });
}

// Swiper initialization for card carousel (if needed)
function initializeSwiper() {
    if (window.Swiper && document.querySelector('.swiper')) {
        new Swiper('.swiper', {
            slidesPerView: 'auto',
            spaceBetween: 20,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            breakpoints: {
                640: {
                    slidesPerView: 2,
                },
                768: {
                    slidesPerView: 3,
                },
                1024: {
                    slidesPerView: 4,
                },
            },
        });
    }
}

// Enhanced toast with icons
function showToastEnhanced(message, type = 'info', duration = 3000) {
    const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        info: 'fas fa-info-circle',
        warning: 'fas fa-exclamation-triangle'
    };

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <i class="${icons[type]}"></i>
        <span>${message}</span>
    `;

    document.getElementById('toast-container').appendChild(toast);

    // Remove toast after specified duration
    setTimeout(() => {
        toast.style.animation = 'toastSlideOut 0.3s ease forwards';
        setTimeout(() => toast.remove(), 300);
    }, duration);
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();
        }

        // Ctrl/Cmd + N for new reel
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            openAddModalEnhanced();
        }
    });
}

// Initialize all enhanced features
function initializeEnhancedFeatures() {
    setupFormValidation();
    setupKeyboardShortcuts();
    initializeSwiper();
    setupImageErrorHandling();
    setupPerformanceOptimizations();
}

// Handle image loading errors
function setupImageErrorHandling() {
    document.addEventListener('error', function(e) {
        if (e.target.tagName === 'IMG') {
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjMUExQTJFIi8+CjxwYXRoIGQ9Ik0xNTAgMTUwQzE2MS4wNDYgMTUwIDE3MCAyNDEuMDQ2IDE3MCAyNTJDMTcwIDI2Mi45NTQgMTYxLjA0NiAyNzIgMTUwIDI3MkMxMzguOTU0IDI3MiAxMzAgMjYyLjk1NCAxMzAgMjUyQzEzMCAyNDEuMDQ2IDEzOC45NTQgMTUwIDE1MCAxNTBaIiBmaWxsPSIjNjY3RUVBIi8+CjxwYXRoIGQ9Ik0xMjAgMjAwSDE4MFYyMjBIMTIwVjIwMFoiIGZpbGw9IiM2NjdFRUEiLz4KPHR5cGUgZm9udC1mYW1pbHk9IkludGVyIiBmb250LXNpemU9IjE0IiBmaWxsPSIjQjhCOEQxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiB4PSIxNTAiIHk9IjMzMCI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pgo8L3N2Zz4K';
            e.target.alt = 'Image not found';
            e.target.style.opacity = '0.5';
        }
    }, true);
}

// Performance optimizations
function setupPerformanceOptimizations() {
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Debounce resize events
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            // Recalculate layouts if needed
            if (window.innerWidth !== window.lastWidth) {
                window.lastWidth = window.innerWidth;
                // Trigger any necessary layout recalculations
            }
        }, 250);
    });
}

// Export/Import functionality
function exportData() {
    const data = JSON.stringify(reelsData, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `instagram-reels-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    showToastEnhanced('Data exported successfully!', 'success');
}

function importData(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importedData = JSON.parse(e.target.result);
            if (Array.isArray(importedData)) {
                if (confirm('This will replace your current collection. Continue?')) {
                    reelsData = importedData;
                    saveReelsData();
                    renderCardsEnhanced();
                    renderFilterTags();
                    showToastEnhanced('Data imported successfully!', 'success');
                }
            } else {
                showToastEnhanced('Invalid file format', 'error');
            }
        } catch (error) {
            showToastEnhanced('Error reading file', 'error');
        }
    };
    reader.readAsText(file);
}

// Add keyboard navigation for accessibility
function setupAccessibility() {
    // Focus management for modals
    const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';

    function trapFocus(modal) {
        const focusableContent = modal.querySelectorAll(focusableElements);
        const firstFocusableElement = focusableContent[0];
        const lastFocusableElement = focusableContent[focusableContent.length - 1];

        modal.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusableElement) {
                        lastFocusableElement.focus();
                        e.preventDefault();
                    }
                } else {
                    if (document.activeElement === lastFocusableElement) {
                        firstFocusableElement.focus();
                        e.preventDefault();
                    }
                }
            }
        });
    }

    // Apply focus trap to modals
    trapFocus(addModal);
    trapFocus(detailModal);
}

// Custom Confirmation Modal
let confirmCallback = null;

function showConfirmModal(title, message, onConfirm) {
    document.getElementById('confirm-title').textContent = title;
    document.getElementById('confirm-message').textContent = message;
    confirmCallback = onConfirm;

    const confirmModal = document.getElementById('confirm-modal');
    confirmModal.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Setup button events
    document.getElementById('confirm-cancel').onclick = closeConfirmModal;
    document.getElementById('confirm-delete').onclick = () => {
        if (confirmCallback) confirmCallback();
        closeConfirmModal();
    };
}

function closeConfirmModal() {
    const confirmModal = document.getElementById('confirm-modal');
    confirmModal.classList.remove('active');
    document.body.style.overflow = 'auto';
    confirmCallback = null;
}

// Floating Instagram Icons
function initializeFloatingIcons() {
    const container = document.getElementById('floating-icons');

    function createFloatingIcon() {
        const icon = document.createElement('i');
        icon.className = 'fab fa-instagram floating-icon';
        icon.style.left = Math.random() * 100 + '%';
        icon.style.fontSize = (Math.random() * 15 + 15) + 'px';
        icon.style.animationDuration = (Math.random() * 10 + 10) + 's';
        icon.style.animationDelay = Math.random() * 5 + 's';

        container.appendChild(icon);

        // Remove icon after animation
        setTimeout(() => {
            if (icon.parentNode) {
                icon.parentNode.removeChild(icon);
            }
        }, 20000);
    }

    // Create initial icons
    for (let i = 0; i < 15; i++) {
        setTimeout(() => createFloatingIcon(), i * 300);
    }

    // Continue creating icons
    setInterval(createFloatingIcon, 2000);
}

// Mouse Follower Effect (Enhanced version)
function initializeMouseFollower() {
    // This is now handled in initializeCustomEffects() to avoid duplication
    console.log('Mouse follower will be initialized with custom effects');
}

// Custom effects implementation (replacing Shery.js)
function initializeCustomEffects() {
    // Enhanced mouse follower
    const follower = document.getElementById('mouse-follower');
    if (follower) {
        let mouseX = 0;
        let mouseY = 0;
        let followerX = 0;
        let followerY = 0;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
            follower.classList.add('active');
        });

        document.addEventListener('mouseleave', () => {
            follower.classList.remove('active');
        });

        function updateFollower() {
            followerX += (mouseX - followerX) * 0.15;
            followerY += (mouseY - followerY) * 0.15;

            follower.style.left = followerX - 15 + 'px';
            follower.style.top = followerY - 15 + 'px';

            requestAnimationFrame(updateFollower);
        }

        updateFollower();
    }

    // Logo text animation
    const logo = document.querySelector('.logo h1');
    if (logo) {
        logo.style.animation = 'fadeInUp 1s ease 0.5s both';
    }

    // Card hover effects
    document.addEventListener('mouseover', (e) => {
        if (e.target.closest('.reel-card')) {
            const card = e.target.closest('.reel-card');
            card.style.transform = 'translateY(-10px) scale(1.02)';
        }
    });

    document.addEventListener('mouseout', (e) => {
        if (e.target.closest('.reel-card')) {
            const card = e.target.closest('.reel-card');
            card.style.transform = 'translateY(0) scale(1)';
        }
    });

    // Smooth scroll reveal for cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'cardSlideIn 0.6s ease forwards';
                cardObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe existing cards
    document.querySelectorAll('.reel-card').forEach(card => {
        cardObserver.observe(card);
    });

    console.log('Custom effects initialized successfully');
}

// Typing Animation with 7 Different Styles
function initializeTypingAnimation() {
    const typingText = document.getElementById('typing-text');
    const messages = [
        "Install Instagram for best experience",
        "Get Instagram for amazing reels",
        "Download Instagram for full features",
        "Install Insta for better viewing",
        "Get the Instagram app now",
        "Install Instagram for smooth experience",
        "Download Insta for best results"
    ];

    const styles = [
        'typing-style-1', // Classic typewriter
        'typing-style-2', // Gradient with scale
        'typing-style-3', // Glow effect with fade
        'typing-style-4', // 3D flip with uppercase
        'typing-style-5', // Blur to focus with gradient
        'typing-style-6', // Step-by-step monospace
        'typing-style-7'  // Rainbow bounce
    ];

    let currentIndex = 0;

    function typeMessage() {
        // Clear previous classes
        typingText.className = '';

        // Set new message and style
        typingText.textContent = messages[currentIndex];
        typingText.classList.add(styles[currentIndex]);

        // Move to next style
        currentIndex = (currentIndex + 1) % styles.length;
    }

    // Start with first message
    typeMessage();

    // Change message every 5 seconds
    setInterval(typeMessage, 5000);
}

// Enhanced typing effect with cursor
function addTypingCursor() {
    const style = document.createElement('style');
    style.textContent = `
        .typing-cursor::after {
            content: '|';
            animation: blink 1s infinite;
            color: #E4405F;
        }
    `;
    document.head.appendChild(style);
}

// Video Preview Setup
function setupVideoPreview(reel) {
    const videoContainer = document.getElementById('video-container');
    const videoElement = document.getElementById('detail-video');
    const videoSource = document.getElementById('video-source');
    const videoFallback = document.getElementById('video-fallback');
    const showVideoBtn = document.getElementById('show-video-btn');

    if (reel.videoLink && reel.videoLink.trim()) {
        // Show video button
        showVideoBtn.style.display = 'flex';

        // Try to process video link
        const processedLink = processVideoLink(reel.videoLink);

        if (processedLink === null || reel.videoLink.includes('mega.nz')) {
            // For Mega links or failed processing, show fallback immediately
            console.log('Showing fallback for:', reel.videoLink);
            videoFallback.style.display = 'flex';
            videoElement.style.display = 'none';
            setupVideoFallback(reel.videoLink);
        } else {
            // Try to load the processed video
            videoSource.src = processedLink;
            videoElement.style.display = 'block';
            videoFallback.style.display = 'none';

            // Handle video load events
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('Video loaded successfully');
                videoFallback.style.display = 'none';
                videoElement.style.display = 'block';
            });

            videoElement.addEventListener('error', function() {
                console.log('Video failed to load, showing fallback');
                videoElement.style.display = 'none';
                videoFallback.style.display = 'flex';
                setupVideoFallback(reel.videoLink);
            });

            // Load the video
            videoElement.load();
        }
    } else {
        // Hide video button if no video link
        showVideoBtn.style.display = 'none';
    }
}

// Process video link for different cloud services
function processVideoLink(link) {
    console.log('Processing video link:', link);

    // Handle Mega.nz links
    if (link.includes('mega.nz')) {
        console.log('Mega.nz link detected');
        // Mega links usually don't work directly in video tags due to CORS
        // We'll show fallback immediately for Mega links
        return null; // This will trigger the fallback
    }

    // Handle Google Drive links
    if (link.includes('drive.google.com')) {
        console.log('Google Drive link detected');
        const fileId = extractGoogleDriveFileId(link);
        if (fileId) {
            return `https://drive.google.com/uc?export=download&id=${fileId}`;
        }
    }

    // Handle Dropbox links
    if (link.includes('dropbox.com')) {
        console.log('Dropbox link detected');
        return link.replace('?dl=0', '?dl=1');
    }

    // Handle direct video links (mp4, webm, etc.)
    if (link.match(/\.(mp4|webm|ogg|avi|mov)(\?.*)?$/i)) {
        console.log('Direct video link detected');
        return link;
    }

    // For unknown services, try original link but expect it might fail
    console.log('Unknown service, trying original link');
    return link;
}

// Extract Google Drive file ID
function extractGoogleDriveFileId(url) {
    const match = url.match(/\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
}

// Setup video fallback
function setupVideoFallback(videoLink) {
    const directVideoBtn = document.getElementById('direct-video-btn');
    directVideoBtn.onclick = () => {
        window.open(videoLink, '_blank');
    };
}

// Media Toggle Setup
function setupMediaToggle(reel) {
    const showImageBtn = document.getElementById('show-image-btn');
    const showVideoBtn = document.getElementById('show-video-btn');
    const imageContainer = document.querySelector('.image-container');
    const videoContainer = document.getElementById('video-container');

    // Reset to image view
    showImageView();

    showImageBtn.onclick = showImageView;
    showVideoBtn.onclick = showVideoView;

    function showImageView() {
        imageContainer.style.display = 'flex';
        videoContainer.style.display = 'none';
        showImageBtn.classList.add('active');
        showVideoBtn.classList.remove('active');
    }

    function showVideoView() {
        if (reel.videoLink && reel.videoLink.trim()) {
            imageContainer.style.display = 'none';
            videoContainer.style.display = 'flex';
            showVideoBtn.classList.add('active');
            showImageBtn.classList.remove('active');
        }
    }
}

// Video embed validation
function validateVideoEmbed(embedCode) {
    if (!embedCode) return true; // Optional field

    // Check if it's a valid iframe with mega.nz embed
    const iframeRegex = /<iframe[^>]*src=["']https:\/\/mega\.nz\/embed\/[^"']*["'][^>]*>/i;
    return iframeRegex.test(embedCode);
}

// Video Embed Setup
function setupVideoEmbed(reel) {
    const videoContainer = document.getElementById('video-container');
    const embedContainer = document.getElementById('mega-embed-container');
    const showVideoBtn = document.getElementById('show-video-btn');

    if (reel.videoEmbed && reel.videoEmbed.trim()) {
        // Show video button
        showVideoBtn.style.display = 'flex';

        // Insert embed code
        embedContainer.innerHTML = reel.videoEmbed;

        // Make iframe responsive
        const iframe = embedContainer.querySelector('iframe');
        if (iframe) {
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '12px';
        }
    } else {
        // Hide video button if no embed code
        showVideoBtn.style.display = 'none';
        embedContainer.innerHTML = '';
    }
}

// Media Toggle Setup (Updated)
function setupMediaToggle(reel) {
    const showImageBtn = document.getElementById('show-image-btn');
    const showVideoBtn = document.getElementById('show-video-btn');
    const imageContainer = document.querySelector('.image-container');
    const videoContainer = document.getElementById('video-container');

    // Reset to image view
    showImageView();

    showImageBtn.onclick = showImageView;
    showVideoBtn.onclick = showVideoView;

    function showImageView() {
        imageContainer.style.display = 'flex';
        videoContainer.style.display = 'none';
        showImageBtn.classList.add('active');
        showVideoBtn.classList.remove('active');
    }

    function showVideoView() {
        if (reel.videoEmbed && reel.videoEmbed.trim()) {
            imageContainer.style.display = 'none';
            videoContainer.style.display = 'flex';
            showVideoBtn.classList.add('active');
            showImageBtn.classList.remove('active');
        }
    }
}

// Video Reels Section
function renderVideoReels() {
    const videoReelsSection = document.getElementById('video-reels-section');
    const videoReelsGrid = document.getElementById('video-reels-grid');
    const viewToggle = document.getElementById('view-toggle');
    const headerVideoBtn = document.getElementById('header-video-reels-btn');

    // Filter reels with video embeds
    const videoReels = reelsData.filter(reel => reel.videoEmbed && reel.videoEmbed.trim());

    if (videoReels.length > 0) {
        viewToggle.style.display = 'block';
        headerVideoBtn.style.display = 'flex';
        videoReelsGrid.innerHTML = '';

        videoReels.forEach(reel => {
            const videoCard = createVideoReelCard(reel);
            videoReelsGrid.appendChild(videoCard);
        });
    } else {
        viewToggle.style.display = 'none';
        headerVideoBtn.style.display = 'none';
        videoReelsSection.style.display = 'none';
    }
}

function createVideoReelCard(reel) {
    const card = document.createElement('div');
    card.className = 'video-reel-card';

    card.innerHTML = `
        <div class="video-reel-embed">
            ${reel.videoEmbed}
        </div>
        <div class="video-reel-info">
            <h3 class="video-reel-title">${reel.title}</h3>
            <div class="video-reel-tags">
                ${reel.tags.map(tag => `<span class="video-reel-tag">${tag}</span>`).join('')}
            </div>
            <div class="video-reel-actions">
                <button class="reel-action-btn primary" onclick="window.open('${reel.url}', '_blank')">
                    <i class="fab fa-instagram"></i>
                    Instagram
                </button>
                <button class="reel-action-btn secondary" onclick="copyToClipboard('${reel.url}')">
                    <i class="fas fa-copy"></i>
                    Copy
                </button>
                <button class="reel-action-btn secondary" onclick="openDetailModal('${reel.id}')">
                    <i class="fas fa-eye"></i>
                    Details
                </button>
            </div>
        </div>
    `;

    // Make iframe responsive
    const iframe = card.querySelector('iframe');
    if (iframe) {
        iframe.style.width = '100%';
        iframe.style.height = '100%';
    }

    return card;
}

// View Toggle Functions
function showVideoReelsView() {
    document.getElementById('cards-grid').style.display = 'none';
    document.getElementById('video-reels-section').style.display = 'block';
    document.getElementById('view-toggle').style.display = 'none';
}

function showAllCardsView() {
    document.getElementById('cards-grid').style.display = 'grid';
    document.getElementById('video-reels-section').style.display = 'none';
    document.getElementById('view-toggle').style.display = 'block';
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', function() {
    setupAccessibility();
});
