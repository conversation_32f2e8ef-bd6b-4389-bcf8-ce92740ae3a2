/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-modal: rgba(0, 0, 0, 0.8);
    
    --text-primary: #ffffff;
    --text-secondary: #b8b8d1;
    --text-muted: #8b8ba7;
    
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.5);
    
    /* Spacing */
    --container-padding: 2rem;
    --border-radius: 20px;
    --border-radius-small: 12px;
    
    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Liquid Background Animation */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    animation: liquidMove 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes liquidMove {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-animation {
    text-align: center;
}

.liquid-loader {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-gradient);
    margin: 0 auto 1rem;
    animation: liquidPulse 2s ease-in-out infinite;
}

@keyframes liquidPulse {
    0%, 100% { transform: scale(1); border-radius: 50%; }
    50% { transform: scale(1.2); border-radius: 30%; }
}

/* Header */
.header {
    padding: 2rem 0;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
    transition: var(--transition-smooth);
}

.logo:hover {
    transform: scale(1.02);
}

.logo i {
    font-size: 2rem;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.5rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.install-message {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(228, 64, 95, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid rgba(228, 64, 95, 0.2);
    min-height: 40px;
    overflow: hidden;
}

#typing-text {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    border-right: 2px solid #E4405F;
    animation: blink 1s infinite;
}

/* Typing Animation Styles */
.typing-style-1 {
    animation: typeWriter1 4s steps(40, end), blink 1s infinite;
}

.typing-style-2 {
    animation: typeWriter2 3s ease-in-out, blink 1s infinite;
    background: linear-gradient(45deg, #E4405F, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.typing-style-3 {
    animation: typeWriter3 3.5s ease-out, blink 1s infinite;
    text-shadow: 0 0 10px rgba(228, 64, 95, 0.5);
}

.typing-style-4 {
    animation: typeWriter4 4s cubic-bezier(0.25, 0.46, 0.45, 0.94), blink 1s infinite;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.typing-style-5 {
    animation: typeWriter5 3s ease-in, blink 1s infinite;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 100%;
    animation: typeWriter5 3s ease-in, gradientShift 2s ease-in-out infinite, blink 1s infinite;
}

.typing-style-6 {
    animation: typeWriter6 4.5s ease-out, blink 1s infinite;
    font-family: 'Courier New', monospace;
    color: #4CAF50;
}

.typing-style-7 {
    animation: typeWriter7 3s ease-in-out, blink 1s infinite;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 400% 400%;
    animation: typeWriter7 3s ease-in-out, rainbowShift 3s ease-in-out infinite, blink 1s infinite;
}

/* Typing Keyframes */
@keyframes typeWriter1 {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes typeWriter2 {
    0% { width: 0; transform: scale(0.8); }
    50% { transform: scale(1.1); }
    100% { width: 100%; transform: scale(1); }
}

@keyframes typeWriter3 {
    0% { width: 0; opacity: 0; }
    50% { opacity: 1; }
    100% { width: 100%; opacity: 1; }
}

@keyframes typeWriter4 {
    0% { width: 0; transform: rotateX(90deg); }
    50% { transform: rotateX(0deg); }
    100% { width: 100%; }
}

@keyframes typeWriter5 {
    0% { width: 0; filter: blur(5px); }
    50% { filter: blur(2px); }
    100% { width: 100%; filter: blur(0px); }
}

@keyframes typeWriter6 {
    0% { width: 0; }
    25% { width: 25%; }
    50% { width: 50%; }
    75% { width: 75%; }
    100% { width: 100%; }
}

@keyframes typeWriter7 {
    0% { width: 0; transform: translateY(20px); }
    50% { transform: translateY(-5px); }
    100% { width: 100%; transform: translateY(0); }
}

@keyframes blink {
    0%, 50% { border-color: #E4405F; }
    51%, 100% { border-color: transparent; }
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes rainbowShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 200% 50%; }
    75% { background-position: 300% 50%; }
    100% { background-position: 400% 50%; }
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-nav-btn {
    background: var(--accent-gradient);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-small);
    color: white;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
}

.header-nav-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
    background: var(--secondary-gradient);
}

.add-btn {
    background: var(--primary-gradient);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-small);
    color: white;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-smooth);
    box-shadow: var(--shadow-light);
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

/* Search Section */
.search-section {
    margin: 2rem 0;
}

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-container i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

#search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    font-size: 1rem;
    backdrop-filter: blur(20px);
    transition: var(--transition-smooth);
}

#search-input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: center;
}

.filter-tag {
    padding: 0.5rem 1rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    backdrop-filter: blur(20px);
}

.filter-tag:hover,
.filter-tag.active {
    background: var(--accent-gradient);
    color: white;
    transform: translateY(-2px);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    max-width: 500px;
    margin: 0 auto;
}

.empty-animation i {
    font-size: 4rem;
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.empty-state h2 {
    margin: 1rem 0;
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
}

.empty-state p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.empty-add-btn {
    background: var(--secondary-gradient);
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-small);
    color: white;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-bounce);
    box-shadow: var(--shadow-light);
}

.empty-add-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy);
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
    padding: 2rem 0;
}

.reel-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    transition: var(--transition-smooth);
    cursor: pointer;
    position: relative;
}

.reel-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(102, 126, 234, 0.3);
}

.card-image {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.reel-card:hover .card-image {
    transform: scale(1.05);
}

.card-content {
    padding: 0.75rem;
}

.card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.card-tag {
    padding: 0.25rem 0.75rem;
    background: rgba(102, 126, 234, 0.2);
    color: var(--text-secondary);
    border-radius: 15px;
    font-size: 0.8rem;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-modal);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-heavy);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.modal-header h2 {
    font-family: 'Poppins', sans-serif;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.close-btn:hover {
    color: var(--text-primary);
    transform: rotate(90deg);
}

/* Form Styles */
.add-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input {
    padding: 0.75rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-smooth);
}

.form-group input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
}

.form-group small {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.cancel-btn,
.submit-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius-small);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.cancel-btn {
    background: var(--bg-card);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.submit-btn {
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --container-padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 0.75rem;
    }

    .card-image {
        height: 200px;
    }
    
    .modal-content {
        padding: 1.5rem;
        width: 95%;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

/* Detail Modal Styles */
.detail-modal .modal-content {
    max-width: 1000px;
    width: 95%;
    padding: 0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.detail-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.detail-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: rotate(90deg);
}

.detail-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
}

.detail-left {
    position: relative;
    overflow: hidden;
    background: var(--bg-primary);
}

.media-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.image-container,
.video-container {
    position: relative;
    width: 100%;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

#detail-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    animation: zoomPulse 4s ease-in-out infinite;
}

@keyframes zoomPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.zoom-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 30%, rgba(102, 126, 234, 0.1) 70%);
    animation: overlayPulse 4s ease-in-out infinite;
}

@keyframes overlayPulse {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Mega Embed Styles */
.mega-embed-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mega-embed-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: var(--border-radius-small);
    background: var(--bg-card);
}

.video-fallback {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-card);
    border-radius: var(--border-radius-small);
}

.fallback-content {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
}

.fallback-content i {
    font-size: 4rem;
    margin-bottom: 1rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.fallback-content h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.fallback-content p {
    margin-bottom: 1.5rem;
    font-size: 1rem;
    color: var(--text-secondary);
}

.fallback-content small {
    display: block;
    margin-top: 0.5rem;
    color: var(--text-muted);
    font-size: 0.8rem;
}

.fallback-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    min-width: 150px;
    box-shadow: var(--shadow-light);
}

.fallback-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy);
    background: var(--secondary-gradient);
}

.fallback-btn i {
    font-size: 1.1rem;
}

/* Media Toggle Buttons */
.media-toggle {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.toggle-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.toggle-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.toggle-btn.active {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
}

.toggle-btn i {
    font-size: 0.9rem;
}

/* Video Reels Section */
.video-reels-section {
    margin: 2rem 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.8rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-header h2 i {
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.view-toggle-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.view-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.view-toggle {
    text-align: center;
    margin: 2rem 0;
}

/* Video Reels Grid */
.video-reels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
    padding: 1rem 0;
    justify-items: center;
}

.video-reel-card {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    transition: var(--transition-smooth);
    position: relative;
    width: 220px;
    max-width: 100%;
}

.video-reel-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: rgba(102, 126, 234, 0.3);
}

.video-reel-embed {
    width: 100%;
    height: 390px; /* 9:16 aspect ratio for 220px width */
    position: relative;
    background: var(--bg-primary);
}

.video-reel-embed iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.video-reel-info {
    padding: 0.75rem;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-reel-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.3;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.video-reel-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
    min-height: 20px;
}

.video-reel-tag {
    padding: 0.2rem 0.5rem;
    background: rgba(102, 126, 234, 0.2);
    color: var(--text-secondary);
    border-radius: 10px;
    font-size: 0.7rem;
    white-space: nowrap;
}

.video-reel-actions {
    display: flex;
    gap: 0.25rem;
    margin-top: auto;
}

.reel-action-btn {
    flex: 1;
    padding: 0.4rem 0.2rem;
    border: none;
    border-radius: var(--border-radius-small);
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.2rem;
    font-size: 0.7rem;
    min-height: 32px;
}

.reel-action-btn.primary {
    background: var(--primary-gradient);
    color: white;
}

.reel-action-btn.secondary {
    background: var(--bg-primary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.reel-action-btn:hover {
    transform: translateY(-1px);
}

.detail-right {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.detail-info h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.detail-tag {
    padding: 0.5rem 1rem;
    background: var(--accent-gradient);
    color: white;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.detail-url {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border-color);
}

.detail-url i {
    color: #E4405F;
}

.detail-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: var(--border-radius-small);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--transition-smooth);
    text-decoration: none;
}

.action-btn.primary {
    background: var(--primary-gradient);
    color: white;
}

.action-btn.secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.action-btn.download {
    background: var(--accent-gradient);
    color: white;
}

.action-btn.danger {
    background: var(--secondary-gradient);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.action-btn .favicon {
    width: 16px;
    height: 16px;
}

/* Install Suggestion in Detail Modal */
.install-suggestion {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: rgba(33, 150, 243, 0.1);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-small);
    border: 1px solid rgba(33, 150, 243, 0.2);
    margin: 0.5rem 0;
    animation: slideInInfo 2s ease-in-out infinite;
}

.install-suggestion i {
    color: #2196F3;
    font-size: 1rem;
}

.install-suggestion span {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
}

@keyframes slideInInfo {
    0%, 100% {
        background: rgba(33, 150, 243, 0.1);
        border-color: rgba(33, 150, 243, 0.2);
    }
    50% {
        background: rgba(33, 150, 243, 0.15);
        border-color: rgba(33, 150, 243, 0.3);
    }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-small);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    backdrop-filter: blur(20px);
    animation: toastSlideIn 0.3s ease;
    max-width: 300px;
}

.toast.success {
    border-left: 4px solid #4CAF50;
}

.toast.error {
    border-left: 4px solid #f44336;
}

.toast.info {
    border-left: 4px solid #2196F3;
}

@keyframes toastSlideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes toastSlideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

.toast {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toast i {
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    :root {
        --container-padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .logo {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .install-message {
        padding: 0.4rem 0.8rem;
        min-height: 35px;
    }

    #typing-text {
        font-size: 0.7rem;
    }

    .video-reels-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 0.75rem;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .video-reel-card {
        width: 160px;
    }

    .video-reel-embed {
        height: 285px; /* 9:16 ratio for 160px width */
    }

    .video-reel-info {
        padding: 0.4rem;
        min-height: 80px;
    }

    .video-reel-title {
        font-size: 0.7rem;
        -webkit-line-clamp: 1;
    }

    .video-reel-tag {
        font-size: 0.55rem;
        padding: 0.1rem 0.25rem;
    }

    .reel-action-btn {
        font-size: 0.55rem;
        padding: 0.2rem 0.05rem;
        gap: 0.1rem;
    }

    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .modal-content {
        padding: 1.5rem;
        width: 95%;
    }

    .form-actions {
        flex-direction: column;
    }

    .detail-modal .modal-content {
        width: 95%;
        max-height: 85vh;
        margin: 1rem auto;
    }

    .detail-layout {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .detail-left {
        height: 220px;
        min-height: 220px;
        max-height: 220px;
        flex-shrink: 0;
    }

    .media-toggle {
        bottom: 0.5rem;
        padding: 0.25rem;
    }

    .toggle-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
    }

    .detail-right {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
        min-height: 0;
    }

    .toast-container {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }

    .toast {
        max-width: none;
    }
}

@media (max-width: 480px) {
    .cards-grid {
        grid-template-columns: 1fr;
    }

    .card-image {
        height: 220px;
    }

    .empty-state {
        padding: 2rem 1rem;
    }

    .empty-state h2 {
        font-size: 1.5rem;
    }

    .detail-right {
        padding: 1.5rem;
    }

    .detail-actions {
        gap: 0.75rem;
    }
}

/* Floating Instagram Icons Background */
.floating-icons {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.floating-icon {
    position: absolute;
    font-size: 20px;
    color: rgba(228, 64, 95, 0.1);
    animation: floatIcon 15s linear infinite;
    pointer-events: none;
}

@keyframes floatIcon {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Mouse Follower */
.mouse-follower {
    position: fixed;
    width: 30px;
    height: 30px;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    opacity: 0;
}

.mouse-follower i {
    font-size: 20px;
    color: rgba(228, 64, 95, 0.6);
    filter: drop-shadow(0 0 10px rgba(228, 64, 95, 0.3));
}

.mouse-follower.active {
    opacity: 1;
    animation: pulse 2s ease-in-out infinite;
}

/* Custom Confirmation Modal */
.confirm-modal .modal-content {
    max-width: 400px;
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
}

.confirm-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #ff6b6b, #ffa500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s ease-in-out infinite;
}

.confirm-icon i {
    font-size: 2rem;
    color: white;
}

.confirm-content h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.confirm-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1rem;
}

.confirm-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.confirm-btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: var(--border-radius-small);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: 1rem;
    min-width: 100px;
}

.confirm-btn.cancel {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.confirm-btn.cancel:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.confirm-btn.delete {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
}

.confirm-btn.delete:hover {
    background: linear-gradient(135deg, #ff5252, #f44336);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

/* Custom Animation Fallbacks */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Shery.js Effects */
.shery-imageMask {
    overflow: hidden;
}

/* Enhanced Hover Effects */
.interactive-hover {
    transition: var(--transition-smooth);
}

.interactive-hover:hover {
    transform: translateY(-5px);
    filter: brightness(1.1);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-primary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-card) 25%, rgba(255,255,255,0.1) 50%, var(--bg-card) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s infinite;
}

@keyframes skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.skeleton {
    background: linear-gradient(90deg, var(--bg-card) 25%, rgba(255,255,255,0.1) 50%, var(--bg-card) 75%);
    background-size: 200% 100%;
    animation: skeleton 1.5s infinite;
}

@keyframes skeleton {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Enhanced Card Styles */
.card-image-container {
    position: relative;
    overflow: hidden;
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.card-overlay i {
    font-size: 3rem;
    color: white;
    transform: scale(0.8);
    transition: var(--transition-smooth);
}

.reel-card:hover .card-overlay {
    opacity: 1;
}

.reel-card:hover .card-overlay i {
    transform: scale(1);
}

.card-meta {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
}

.card-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Enhanced Modal Animations */
@keyframes modalBounceIn {
    0% {
        transform: scale(0.3) translateY(-50px);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateY(0);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes tagSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

/* Swiper Customization */
.swiper {
    width: 100%;
    height: 100%;
}

.swiper-slide {
    text-align: center;
    font-size: 18px;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
}

.swiper-pagination-bullet {
    background: var(--text-secondary);
    opacity: 0.5;
}

.swiper-pagination-bullet-active {
    background: var(--primary-gradient);
    opacity: 1;
}

.swiper-button-next,
.swiper-button-prev {
    color: var(--text-primary);
    background: var(--bg-card);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    margin-top: -20px;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 16px;
}

/* Micro-interactions */
.interactive-element {
    transition: var(--transition-smooth);
}

.interactive-element:hover {
    transform: translateY(-2px);
}

.interactive-element:active {
    transform: translateY(0) scale(0.98);
}

/* Enhanced Form Styles */
.form-group input:focus {
    transform: translateY(-2px);
}

.form-group input.error {
    border-color: #f44336;
    animation: shake 0.5s ease-in-out;
}

.form-group input.success {
    border-color: #4CAF50;
}

/* Loading Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Glassmorphism Effects */
.glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Particle Animation Background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    animation: particleFloat 10s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .video-reels-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .video-reel-card {
        width: 200px;
    }

    .video-reel-embed {
        height: 355px; /* 9:16 ratio for 200px */
    }

    .video-reel-info {
        padding: 0.5rem;
        min-height: 100px;
    }

    .video-reel-title {
        font-size: 0.8rem;
    }

    .video-reel-tag {
        font-size: 0.65rem;
        padding: 0.15rem 0.4rem;
    }

    .reel-action-btn {
        font-size: 0.65rem;
        padding: 0.3rem 0.1rem;
    }
}

@media (max-width: 992px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }

    .video-reels-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }

    .video-reel-card {
        width: 180px;
    }

    .video-reel-embed {
        height: 320px; /* 9:16 ratio for 180px */
    }

    .video-reel-info {
        padding: 0.5rem;
        min-height: 90px;
    }

    .video-reel-title {
        font-size: 0.75rem;
    }

    .video-reel-tag {
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }

    .reel-action-btn {
        font-size: 0.6rem;
        padding: 0.25rem 0.1rem;
    }

    .detail-layout {
        grid-template-columns: 1fr;
        grid-template-rows: 300px 1fr;
    }

    .detail-left {
        min-height: 300px;
        max-height: 300px;
    }

    .detail-right {
        padding: 1.5rem;
        min-height: 350px;
    }
}

@media (max-width: 576px) {
    .header {
        padding: 1rem 0;
    }

    .logo h1 {
        font-size: 1.1rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 0.75rem;
        width: 100%;
    }

    .header-nav-btn,
    .add-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        width: 100%;
        justify-content: center;
    }

    .video-reels-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .video-reel-card {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .video-reel-embed {
        height: 480px; /* 9:16 ratio for mobile */
    }

    .video-reel-info {
        padding: 0.75rem;
        min-height: 100px;
    }

    .video-reel-title {
        font-size: 0.9rem;
        -webkit-line-clamp: 2;
    }

    .video-reel-tag {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    .reel-action-btn {
        font-size: 0.8rem;
        padding: 0.5rem 0.25rem;
        gap: 0.25rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .modal-content {
        margin: 0.5rem;
        padding: 1rem;
        max-height: 95vh;
        overflow-y: auto;
    }

    .detail-modal .modal-content {
        margin: 0.5rem;
        padding: 0;
        max-height: 90vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .detail-layout {
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: auto;
    }

    .detail-left {
        height: 180px;
        min-height: 180px;
        max-height: 180px;
        flex-shrink: 0;
    }

    .media-toggle {
        bottom: 0.25rem;
        padding: 0.2rem;
        gap: 0.25rem;
    }

    .toggle-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
    }

    .toggle-btn i {
        font-size: 0.7rem;
    }

    .detail-right {
        flex: 1;
        padding: 0.75rem;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        min-height: 0;
    }

    .detail-info {
        flex-shrink: 0;
        margin-bottom: 1rem;
    }

    .detail-info h2 {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .detail-tags {
        margin-bottom: 0.75rem;
    }

    .detail-tag {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
        margin-right: 0.25rem;
        margin-bottom: 0.25rem;
    }

    .detail-url {
        padding: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.8rem;
    }

    .detail-actions {
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .install-suggestion {
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .install-suggestion span {
        font-size: 0.75rem;
    }

    .action-btn {
        padding: 0.5rem;
        font-size: 0.8rem;
        min-height: 40px;
    }

    .detail-close {
        top: 0.5rem;
        right: 0.5rem;
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }
}
